import { Spark<PERSON> } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { GridBackground } from './GridBackground';
import { useState, useEffect } from 'react';

export function HeroSection() {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 100);
    
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="relative min-h-screen w-full flex flex-col items-center justify-center pt-16 pb-10 overflow-hidden">
      <GridBackground />
      
      <div className={`flex flex-col items-center justify-center z-10 px-4 transition-opacity duration-700 ${isVisible ? 'opacity-100' : 'opacity-0'}`}>
        <div className="flex items-center gap-2 bg-white/5 backdrop-blur-sm px-3 py-1.5 rounded-full mb-6">
          <Sparkles className="h-4 w-4 text-white/80" />
          <span className="text-sm text-white/80">New AI Feature</span>
        </div>
        
        <h1 className="text-[#FFB6B6] text-6xl md:text-8xl lg:text-9xl font-bold mb-6 tracking-tight text-center">
          Aligno
        </h1>
        
        <p className="text-center text-gray-400 max-w-2xl mb-10 text-lg md:text-xl">
          Prioritise What Matters - Streamline Your Workflow and Focus on What Drives Success!
        </p>
        
        <Button 
          className="bg-transparent hover:bg-white/10 text-white border border-white/20 rounded-full px-8 py-6 text-base transition-all duration-300"
        >
          Buy Template
        </Button>
        
        <div className="mt-16 w-full max-w-4xl relative">
          <div className="absolute inset-0 bg-gradient-to-t from-[#0E0D0C] to-transparent z-10 h-[15%] bottom-0"></div>
          <img 
            src="https://images.pexels.com/photos/3182829/pexels-photo-3182829.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2"
            alt="Aligno Dashboard"
            className="w-full h-auto rounded-lg shadow-2xl"
          />
          
          <div className="absolute bottom-4 right-4 flex flex-col items-end gap-3 z-20">
            <button className="bg-white rounded-full text-xs px-4 py-1.5 text-black font-medium flex items-center gap-1">
              Get all templates on <span className="font-bold">Figma</span>
            </button>
            <button className="bg-black border border-white/20 rounded-full text-xs px-4 py-1.5 text-white font-medium">
              Buy this Template
            </button>
            <div className="bg-white rounded-full text-xs px-3 py-1.5 text-black font-medium flex items-center gap-1">
              <span className="text-[0.7rem]">Made in Framer</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}