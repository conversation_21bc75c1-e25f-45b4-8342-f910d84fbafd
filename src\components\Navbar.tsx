import { SquareIcon } from 'lucide-react';

export function Navbar() {
  return (
    <nav className="fixed top-0 left-0 right-0 z-50 px-4 md:px-8 py-4 bg-transparent">
      <div className="max-w-7xl mx-auto flex items-center justify-between">
        <div className="flex-1 flex justify-start">
          <a href="#" className="text-gray-200 hover:text-white transition-colors text-sm md:text-base">
            Features
          </a>
          <a href="#" className="ml-6 md:ml-10 text-gray-200 hover:text-white transition-colors text-sm md:text-base">
            Testimonials
          </a>
        </div>
        
        <div className="flex items-center justify-center">
          <div className="bg-white/10 backdrop-blur-sm p-2 rounded-lg">
            <SquareIcon className="h-5 w-5 text-white" />
          </div>
        </div>
        
        <div className="flex-1 flex justify-end">
          <a href="#" className="text-gray-200 hover:text-white transition-colors text-sm md:text-base">
            Why Aligno?
          </a>
          <a href="#" className="ml-6 md:ml-10 text-gray-200 hover:text-white transition-colors text-sm md:text-base">
            Pricing
          </a>
        </div>
      </div>
    </nav>
  );
}