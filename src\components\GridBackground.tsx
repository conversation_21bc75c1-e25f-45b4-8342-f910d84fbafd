import { useEffect, useState } from 'react';

export function GridBackground() {
  const [mounted, setMounted] = useState(false);
  
  useEffect(() => {
    setMounted(true);
  }, []);
  
  return (
    <div className="absolute inset-0 overflow-hidden">
      <div className={`grid grid-cols-12 h-full w-full transition-opacity duration-1000 ${mounted ? 'opacity-50' : 'opacity-0'}`}>
        {Array.from({ length: 12 }).map((_, colIndex) => (
          <div key={`col-${colIndex}`} className="relative h-full border-r border-white/5">
            {Array.from({ length: 12 }).map((_, rowIndex) => (
              <div 
                key={`dot-${colIndex}-${rowIndex}`} 
                className="absolute w-1 h-1 bg-white/20 rounded-full transform -translate-x-1/2"
                style={{
                  left: '0%',
                  top: `${(rowIndex + 1) * (100 / 13)}%`
                }}
              />
            ))}
          </div>
        ))}
        
        <div className="absolute inset-0 bg-gradient-radial from-[#221D1B]/80 via-transparent to-transparent opacity-90"></div>
        <div className="absolute inset-0 bg-gradient-to-b from-[#0E0D0C] via-transparent to-[#0E0D0C] opacity-90"></div>
      </div>
    </div>
  );
}